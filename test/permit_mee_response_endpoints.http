### 搜索许可证MEE响应列表
POST http://localhost:8080/api/search/permit/mee/response/list
Content-Type: application/json
Authorization: Bearer {{user_jwt_token}}

{
  "username": "test_user"
}

### 搜索许可证MEE响应详情
POST http://localhost:8080/api/search/permit/mee/response/content
Content-Type: application/json
Authorization: Bearer {{user_jwt_token}}

{
  "username": "test_user",
  "data_id": "test_data_id",
  "timestamp": "1234567890"
}

### 测试无效的JWT令牌 - 搜索许可证MEE响应列表
POST http://localhost:8080/api/search/permit/mee/response/list
Content-Type: application/json
Authorization: Bearer invalid_token

{
  "username": "test_user"
}

### 测试缺少参数 - 搜索许可证MEE响应列表
POST http://localhost:8080/api/search/permit/mee/response/list
Content-Type: application/json
Authorization: Bearer {{user_jwt_token}}

{
}

### 测试缺少参数 - 搜索许可证MEE响应详情
POST http://localhost:8080/api/search/permit/mee/response/content
Content-Type: application/json
Authorization: Bearer {{user_jwt_token}}

{
  "username": "test_user"
}
