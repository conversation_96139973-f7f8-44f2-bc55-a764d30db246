package com.dzhp.permit.routers.llm

import com.dzhp.permit.models.*
import com.dzhp.permit.services.PermitAgentService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.plugins.origin
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

/**
 * 代理系统路由
 * 提供代理系统相关的API接口
 *
 * - GET /api/agent/list : 获取代理列表
 * - GET /api/agent/tags : 获取所有代理标签
 * - GET /api/search/permit/mee/response/list : 搜索许可证MEE响应列表（需要用户JWT认证）
 * - GET /api/search/permit/mee/response/content : 搜索许可证MEE响应详情（需要用户JWT认证）
 */
fun Application.permitAgentRouter(permitAgentService: PermitAgentService) {
    val logger = LoggerFactory.getLogger("PermitAgentRouter")
    routing {
        route("/api/agent") {
            /**
             * 获取代理列表
             * 返回所有在线的代理
             */
            get("/list") {
                try {
                    val agents = permitAgentService.searchAgentList()

                    val responseData = PermitAgentListResponse(
                        agents = agents,
                        total = agents.size
                    )

                    call.respond(
                        HttpStatusCode.OK,
                        standardResponse(
                            code = HttpStatusCode.OK.value,
                            message = "获取代理列表成功",
                            data = responseData
                        )
                    )
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "获取代理列表失败: ${e.message}"
                        )
                    )
                }
            }

            /**
             * 获取所有代理标签
             * 返回所有在线代理的标签列表（去重）
             */
            get("/tags") {
                try {
                    val tags = permitAgentService.searchAgentTagList()

                    val responseData = PermitAgentTagsResponse(
                        tags = tags
                    )

                    call.respond(
                        HttpStatusCode.OK,
                        standardResponse(
                            code = HttpStatusCode.OK.value,
                            message = "获取代理标签列表成功",
                            data = responseData
                        )
                    )
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "获取代理标签列表失败: ${e.message}"
                        )
                    )
                }
            }
        }

        // 需要用户JWT认证的许可证MEE响应搜索接口
        authenticate("user-llm-jwt") {
            /**
             * 搜索许可证MEE响应列表
             * 根据用户名获取许可证MEE响应记录列表
             */
            post("/api/search/permit/mee/response/list") {
                val startTime = System.currentTimeMillis()
                val requestBody = try {
                    call.receive<PermitMeeResponseListRequest>()
                } catch (e: Exception) {
                    logger.error("Failed to parse request body: ${e.message}")
                    call.respond(
                        HttpStatusCode.BadRequest,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.BadRequest.value,
                            message = "请求参数格式错误: ${e.message}"
                        )
                    )
                    return@post
                }

                // 从JWT中获取用户信息进行日志记录
                val userId = call.principal<JWTPrincipal>()?.getClaim("code", String::class)?.toString() ?: "unknown"

                logger.info("Request body: $requestBody")

                try {
                    val responses = permitAgentService.searchPermitMeeResponseList(requestBody.username)

                    val responseData = PermitMeeResponseListResponse(
                        responses = responses
                    )

                    val endTime = System.currentTimeMillis()
                    val responseTime = endTime - startTime

                    val standardResp = standardResponse(
                        code = HttpStatusCode.OK.value,
                        message = "获取许可证MEE响应列表成功",
                        data = responseData
                    )

                    logger.info("[${HttpStatusCode.OK.value}] [${responseTime}ms] POST /api/search/permit/mee/response/list")
                    logger.info("Request IP: ${call.request.origin.remoteHost}")
                    logger.info("Response body: $standardResp")

                    call.respond(HttpStatusCode.OK, standardResp)
                } catch (e: Exception) {
                    val endTime = System.currentTimeMillis()
                    val responseTime = endTime - startTime

                    logger.error("Error searching permit MEE response list: ${e.message}", e)

                    val errorResp = standardResponse<EmptyResponseData>(
                        code = HttpStatusCode.InternalServerError.value,
                        message = "获取许可证MEE响应列表失败: ${e.message}"
                    )

                    logger.info("[${HttpStatusCode.InternalServerError.value}] [${responseTime}ms] POST /api/search/permit/mee/response/list")
                    logger.info("Request IP: ${call.request.origin.remoteHost}")
                    logger.info("Response body: $errorResp")

                    call.respond(HttpStatusCode.InternalServerError, errorResp)
                }
            }

            /**
             * 搜索许可证MEE响应详情
             * 根据用户名、数据ID和时间戳获取许可证MEE响应详情
             */
            post("/api/search/permit/mee/response/content") {
                val startTime = System.currentTimeMillis()
                val requestBody = try {
                    call.receive<PermitMeeResponseContentRequest>()
                } catch (e: Exception) {
                    logger.error("Failed to parse request body: ${e.message}")
                    call.respond(
                        HttpStatusCode.BadRequest,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.BadRequest.value,
                            message = "请求参数格式错误: ${e.message}"
                        )
                    )
                    return@post
                }

                // 从JWT中获取用户信息进行日志记录
                val userId = call.principal<JWTPrincipal>()?.getClaim("code", String::class)?.toString() ?: "unknown"

                logger.info("Request body: $requestBody")

                try {
                    val response = permitAgentService.searchPermitMeeResponseByUsernameDataId(
                        requestBody.username,
                        requestBody.data_id,
                        requestBody.timestamp
                    )

                    val responseData = PermitMeeResponseContentResponse(
                        response = response
                    )

                    val endTime = System.currentTimeMillis()
                    val responseTime = endTime - startTime

                    val standardResp = standardResponse(
                        code = HttpStatusCode.OK.value,
                        message = "获取许可证MEE响应详情成功",
                        data = responseData
                    )

                    logger.info("[${HttpStatusCode.OK.value}] [${responseTime}ms] POST /api/search/permit/mee/response/content")
                    logger.info("Request IP: ${call.request.origin.remoteHost}")
                    logger.info("Response body: $standardResp")

                    call.respond(HttpStatusCode.OK, standardResp)
                } catch (e: Exception) {
                    val endTime = System.currentTimeMillis()
                    val responseTime = endTime - startTime

                    logger.error("Error searching permit MEE response content: ${e.message}", e)

                    val errorResp = standardResponse<EmptyResponseData>(
                        code = HttpStatusCode.InternalServerError.value,
                        message = "获取许可证MEE响应详情失败: ${e.message}"
                    )

                    logger.info("[${HttpStatusCode.InternalServerError.value}] [${responseTime}ms] POST /api/search/permit/mee/response/content")
                    logger.info("Request IP: ${call.request.origin.remoteHost}")
                    logger.info("Response body: $errorResp")

                    call.respond(HttpStatusCode.InternalServerError, errorResp)
                }
            }
        }
    }
}
